package com.boryou.web.module.mark.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.exception.CustomException;
import com.boryou.web.module.mark.domain.EsBeanMark;
import com.boryou.web.module.mark.domain.vo.EsBeanMarkPage;
import com.boryou.web.module.mark.domain.vo.EsBeanMarkVO;
import com.boryou.web.module.mark.mapper.EsBeanMarkMapper;
import com.boryou.web.module.material.entity.Material;
import com.boryou.web.module.material.service.MaterialService;
import com.boryou.web.util.JacksonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * EsBeanMark 服务类
 * 模仿 module/warn 下的代码写法
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EsBeanMarkService extends ServiceImpl<EsBeanMarkMapper, EsBeanMark> {

    private final MaterialService materialService;

    /**
     * 分页查询EsBeanMark信息
     *
     * @param esBeanMarkVO 查询参数
     * @return 分页结果
     */
    public Page<EsBeanMarkVO> getEsBeanMarkPage(EsBeanMarkPage esBeanMarkPage, SysUser user) {
        Page<EsBeanMark> page = new Page<>(esBeanMarkPage.getPageNum(), esBeanMarkPage.getPageSize());

        Long userId = user.getUserId();

        Date updateStart = esBeanMarkPage.getUpdateStart();
        Date updateEnd = esBeanMarkPage.getUpdateEnd();
        Date publishStart = esBeanMarkPage.getPublishStart();
        Date publishEnd = esBeanMarkPage.getPublishEnd();

        Integer emotionFlag = esBeanMarkPage.getEmotionFlag();
        List<Integer> types = esBeanMarkPage.getTypes();
        String kw = esBeanMarkPage.getKw();

        List<String> ids = esBeanMarkPage.getIds();

        Page<EsBeanMark> pageResult = this.lambdaQuery()
                .ge(EsBeanMark::getUTime, updateStart)
                .le(EsBeanMark::getUTime, updateEnd)
                .ge(publishStart != null, EsBeanMark::getPublishTime, publishStart)
                .le(publishEnd != null, EsBeanMark::getPublishTime, publishEnd)
                .eq(emotionFlag != null, EsBeanMark::getEmotionFlag, emotionFlag)
                .in(CollUtil.isNotEmpty(types), EsBeanMark::getType, types)
                .in(CollUtil.isNotEmpty(ids), EsBeanMark::getEsBeanMarkId, ids)
                .eq(EsBeanMark::getDelFlag, 0)
                .eq(EsBeanMark::getUserId, userId)
                .orderByDesc(EsBeanMark::getUTime)
                .and(CharSequenceUtil.isNotBlank(kw), t ->
                        t.like(EsBeanMark::getTitle, kw)
                                .or()
                                .like(EsBeanMark::getText, kw)
                ).page(page);

        long total = pageResult.getTotal();
        long current = pageResult.getCurrent();
        long size = pageResult.getSize();

        List<EsBeanMark> records = pageResult.getRecords();

        // 转换为VO
        Page<EsBeanMarkVO> voPage = new Page<>(current, size, total);
        List<EsBeanMarkVO> voList = BeanUtil.copyToList(records, EsBeanMarkVO.class);
        for (EsBeanMarkVO esBeanMarkVO : voList) {
            List<String> hitWord = esBeanMarkVO.getHitWord();
            if (CollUtil.isNotEmpty(hitWord)) {
                esBeanMarkVO.setHitWords(CollUtil.join(hitWord, " "));
            }
        }
        voPage.setRecords(voList);

        return voPage;
    }

    /**
     * 添加或更新EsBeanMark信息
     * 使用 lambdaUpdate().set() 方法，只有传进来的值与原值不一样才set
     *
     * @param esBeanMarkVO EsBeanMark信息
     * @param user         当前用户
     * @return 是否成功
     */
    public boolean saveOrUpdateEsBeanMark(EsBeanMarkVO esBeanMarkVO, SysUser user) {
        String articleId = esBeanMarkVO.getId();
        if (CharSequenceUtil.isBlank(articleId)) {
            throw new CustomException("文章ID不能为空");
        }

        Long userId = user.getUserId();
        String userName = user.getUserName();
        Long deptId = user.getDeptId();

        // 查询是否已存在
        EsBeanMark existEntity = this.lambdaQuery()
                .eq(EsBeanMark::getId, articleId)
                .eq(EsBeanMark::getUserId, userId)
                .one();

        EsBeanMark esBeanMark = BeanUtil.copyProperties(esBeanMarkVO, EsBeanMark.class);

        Date date = new Date();

        if (existEntity != null) {
            Long esBeanMarkId = existEntity.getEsBeanMarkId();
            List<String> hitWord = existEntity.getHitWord();

            String hitWords = esBeanMarkVO.getHitWords();
            if (CharSequenceUtil.isNotBlank(hitWords)) {
                List<String> split = CharSequenceUtil.split(hitWords, " ");
                if (CollUtil.isNotEmpty(hitWord)) {
                    split.addAll(hitWord);
                }
                Set<String> hitWordSql = new HashSet<>(split);
                esBeanMark.setHitWord(CollUtil.newArrayList(hitWordSql));
            }

            LambdaUpdateChainWrapper<EsBeanMark> lambdaSet = this.updateWithLambdaSet(existEntity, esBeanMark);
            lambdaSet.set(EsBeanMark::getUBy, userName)
                    .set(EsBeanMark::getUTime, date)
                    .set(EsBeanMark::getDelFlag, 0);

            // 设置更新条件
            lambdaSet.eq(EsBeanMark::getEsBeanMarkId, esBeanMarkId);
            return lambdaSet.update();
        }

        // 新增
        EsBeanMark entity = BeanUtil.copyProperties(esBeanMarkVO, EsBeanMark.class);
        entity.setEsBeanMarkId(IdUtil.getSnowflakeNextId());
        entity.setDelFlag(0);
        entity.setCreateBy(userName);
        entity.setCreateTime(date);
        entity.setUTime(date);
        entity.setUBy(userName);
        entity.setUserId(Convert.toStr(userId));
        entity.setDeptId(Convert.toStr(deptId));
        return this.save(entity);
    }

    /**
     * 删除EsBeanMark信息（物理删除）
     *
     * @param esBeanMarkVO 删除参数
     * @return 是否成功
     */
    public boolean deleteEsBeanMark(EsBeanMarkVO esBeanMarkVO) {
        Long esBeanMarkId = esBeanMarkVO.getEsBeanMarkId();
        if (esBeanMarkId == null) {
            throw new CustomException("id不能为空");
        }
        return this.lambdaUpdate().set(EsBeanMark::getDelFlag, 1).eq(EsBeanMark::getEsBeanMarkId, esBeanMarkId).update();
    }

    /**
     * 使用 Lambda 表达式更新字段，只有值不同才更新
     * 对于使用 JacksonTypeHandler 的字段，使用 JacksonUtils.toJson() 进行序列化
     *
     * @param existEntity 现有实体
     * @param esBeanMark  新的实体数据
     * @return 是否成功更新
     */
    private LambdaUpdateChainWrapper<EsBeanMark> updateWithLambdaSet(EsBeanMark existEntity, EsBeanMark esBeanMark) {
        return this.lambdaUpdate()
                .set(updateFieldIfDifferent(esBeanMark.getType(), existEntity.getType()), EsBeanMark::getType, esBeanMark.getType())
                .set(updateFieldIfDifferent(esBeanMark.getTypeName(), existEntity.getTypeName()), EsBeanMark::getTypeName, esBeanMark.getTypeName())
                .set(updateFieldIfDifferent(esBeanMark.getPublishTime(), existEntity.getPublishTime()), EsBeanMark::getPublishTime, esBeanMark.getPublishTime())
                .set(updateFieldIfDifferent(esBeanMark.getTitle(), existEntity.getTitle()), EsBeanMark::getTitle, esBeanMark.getTitle())
                .set(updateFieldIfDifferent(esBeanMark.getText(), existEntity.getText()), EsBeanMark::getText, esBeanMark.getText())
                .set(updateFieldIfDifferent(esBeanMark.getSummary(), existEntity.getSummary()), EsBeanMark::getSummary, esBeanMark.getSummary())
                .set(updateFieldIfDifferent(esBeanMark.getUrl(), existEntity.getUrl()), EsBeanMark::getUrl, esBeanMark.getUrl())
                .set(updateFieldIfDifferent(esBeanMark.getHost(), existEntity.getHost()), EsBeanMark::getHost, esBeanMark.getHost())
                .set(updateFieldIfDifferent(esBeanMark.getDomain(), existEntity.getDomain()), EsBeanMark::getDomain, esBeanMark.getDomain())
                .set(updateFieldIfDifferent(esBeanMark.getAuthor(), existEntity.getAuthor()), EsBeanMark::getAuthor, esBeanMark.getAuthor())
                .set(updateFieldIfDifferent(esBeanMark.getAuthorId(), existEntity.getAuthorId()), EsBeanMark::getAuthorId, esBeanMark.getAuthorId())
                .set(updateFieldIfDifferent(esBeanMark.getAuthorSex(), existEntity.getAuthorSex()), EsBeanMark::getAuthorSex, esBeanMark.getAuthorSex())
                .set(updateFieldIfDifferent(esBeanMark.getBizId(), existEntity.getBizId()), EsBeanMark::getBizId, esBeanMark.getBizId())
                .set(updateFieldIfDifferent(esBeanMark.getAccountLevel(), existEntity.getAccountLevel()), EsBeanMark::getAccountLevel, esBeanMark.getAccountLevel())
                .set(updateFieldIfDifferent(esBeanMark.getSiteAreaCode(), existEntity.getSiteAreaCode()), EsBeanMark::getSiteAreaCode, esBeanMark.getSiteAreaCode())
                .set(updateFieldIfDifferent(esBeanMark.getSiteAreaCodeName(), existEntity.getSiteAreaCodeName()), EsBeanMark::getSiteAreaCodeName, esBeanMark.getSiteAreaCodeName())
                // contentAreaCode 字段使用 JacksonTypeHandler，需要序列化为 JSON
                .set(updateFieldIfDifferent(esBeanMark.getContentAreaCode(), existEntity.getContentAreaCode()), EsBeanMark::getContentAreaCode, JacksonUtils.toJson(esBeanMark.getContentAreaCode()))
                .set(updateFieldIfDifferent(esBeanMark.getContentAreaCodeName(), existEntity.getContentAreaCodeName()), EsBeanMark::getContentAreaCodeName, esBeanMark.getContentAreaCodeName())
                // siteMeta 字段使用 JacksonTypeHandler，需要序列化为 JSON
                .set(updateFieldIfDifferent(esBeanMark.getSiteMeta(), existEntity.getSiteMeta()), EsBeanMark::getSiteMeta, JacksonUtils.toJson(esBeanMark.getSiteMeta()))
                // contentMeta 字段使用 JacksonTypeHandler，需要序列化为 JSON
                .set(updateFieldIfDifferent(esBeanMark.getContentMeta(), existEntity.getContentMeta()), EsBeanMark::getContentMeta, JacksonUtils.toJson(esBeanMark.getContentMeta()))
                .set(updateFieldIfDifferent(esBeanMark.getFansNum(), existEntity.getFansNum()), EsBeanMark::getFansNum, esBeanMark.getFansNum())
                .set(updateFieldIfDifferent(esBeanMark.getReadNum(), existEntity.getReadNum()), EsBeanMark::getReadNum, esBeanMark.getReadNum())
                .set(updateFieldIfDifferent(esBeanMark.getCommentNum(), existEntity.getCommentNum()), EsBeanMark::getCommentNum, esBeanMark.getCommentNum())
                .set(updateFieldIfDifferent(esBeanMark.getLikeNum(), existEntity.getLikeNum()), EsBeanMark::getLikeNum, esBeanMark.getLikeNum())
                .set(updateFieldIfDifferent(esBeanMark.getReprintNum(), existEntity.getReprintNum()), EsBeanMark::getReprintNum, esBeanMark.getReprintNum())
                .set(updateFieldIfDifferent(esBeanMark.getSector(), existEntity.getSector()), EsBeanMark::getSector, esBeanMark.getSector())
                // contentForm 字段使用 JacksonTypeHandler，需要序列化为 JSON
                .set(updateFieldIfDifferent(esBeanMark.getContentForm(), existEntity.getContentForm()), EsBeanMark::getContentForm, JacksonUtils.toJson(esBeanMark.getContentForm()))
                .set(updateFieldIfDifferent(esBeanMark.getMd5(), existEntity.getMd5()), EsBeanMark::getMd5, esBeanMark.getMd5())
                .set(updateFieldIfDifferent(esBeanMark.getSrcCodePath(), existEntity.getSrcCodePath()), EsBeanMark::getSrcCodePath, esBeanMark.getSrcCodePath())
                .set(updateFieldIfDifferent(esBeanMark.getCoverUrl(), existEntity.getCoverUrl()), EsBeanMark::getCoverUrl, esBeanMark.getCoverUrl())
                // picUrl 字段使用 JacksonTypeHandler，需要序列化为 JSON
                .set(updateFieldIfDifferent(esBeanMark.getPicUrl(), existEntity.getPicUrl()), EsBeanMark::getPicUrl, JacksonUtils.toJson(esBeanMark.getPicUrl()))
                // avdUrl 字段使用 JacksonTypeHandler，需要序列化为 JSON
                .set(updateFieldIfDifferent(esBeanMark.getAvdUrl(), existEntity.getAvdUrl()), EsBeanMark::getAvdUrl, JacksonUtils.toJson(esBeanMark.getAvdUrl()))
                .set(updateFieldIfDifferent(esBeanMark.getEmotionFlag(), existEntity.getEmotionFlag()), EsBeanMark::getEmotionFlag, esBeanMark.getEmotionFlag())
                .set(updateFieldIfDifferent(esBeanMark.getIsOriginal(), existEntity.getIsOriginal()), EsBeanMark::getIsOriginal, esBeanMark.getIsOriginal())
                .set(updateFieldIfDifferent(esBeanMark.getIsSpam(), existEntity.getIsSpam()), EsBeanMark::getIsSpam, esBeanMark.getIsSpam())
                .set(updateFieldIfDifferent(esBeanMark.getDay(), existEntity.getDay()), EsBeanMark::getDay, esBeanMark.getDay())
                .set(updateFieldIfDifferent(esBeanMark.getIsRead(), existEntity.getIsRead()), EsBeanMark::getIsRead, esBeanMark.getIsRead())
                .set(updateFieldIfDifferent(esBeanMark.getAccountGrade(), existEntity.getAccountGrade()), EsBeanMark::getAccountGrade, esBeanMark.getAccountGrade())
                .set(updateFieldIfDifferent(esBeanMark.getUpdateTime(), existEntity.getUpdateTime()), EsBeanMark::getUpdateTime, esBeanMark.getUpdateTime())
                // hitWord 字段使用 JacksonTypeHandler，需要序列化为 JSON
                .set(updateFieldIfDifferent(esBeanMark.getHitWord(), existEntity.getHitWord()), EsBeanMark::getHitWord, JacksonUtils.toJson(esBeanMark.getHitWord()))
                .set(updateFieldIfDifferent(esBeanMark.getPlanId(), existEntity.getPlanId()), EsBeanMark::getPlanId, esBeanMark.getPlanId());
    }

    /**
     * 判断字段是否需要更新（值不同且新值不为空）
     *
     * @param newValue     新值
     * @param currentValue 当前值
     * @return 是否需要更新
     */
    private boolean updateFieldIfDifferent(Object newValue, Object currentValue) {
        // 如果新值为空，则不更新
        if (newValue == null) {
            return false;
        }

        // 如果新值为空字符串，则不更新
        if (newValue instanceof String && CharSequenceUtil.isBlank((String) newValue)) {
            return false;
        }

        // 比较值是否不同
        return !Objects.equals(newValue, currentValue);
    }

    public void judgeIds(EsBeanMarkPage esBeanMarkPage) {
        List<String> ids = esBeanMarkPage.getIds();
        if (CollUtil.isNotEmpty(ids)) {
            esBeanMarkPage.setPageNum(1);
            esBeanMarkPage.setPageSize(ids.size());
        }
    }

    public boolean addMaterial(List<EsBeanMarkVO> esBeanMarkList, EsBeanMarkPage esBeanMarkPage) {
        List<Material> materialList = new ArrayList<>();
        String folderId = esBeanMarkPage.getFolderId();

        for (EsBeanMarkVO esBeanMarkVO : esBeanMarkList) {
            Material material = buildMaterial(esBeanMarkVO, folderId);
            materialList.add(material);
        }

        return materialService.addBatch(materialList);
    }

    private Material buildMaterial(EsBeanMarkVO esBeanMarkVO, String folderId) {
        Integer type = esBeanMarkVO.getType();
        String typeName = esBeanMarkVO.getTypeName();
        Date publishTime = esBeanMarkVO.getPublishTime();
        String title = esBeanMarkVO.getTitle();
        String text = esBeanMarkVO.getText();
        String url = esBeanMarkVO.getUrl();
        String host = esBeanMarkVO.getHost();
        String author = esBeanMarkVO.getAuthor();
        String siteAreaCodeName = esBeanMarkVO.getSiteAreaCodeName();
        Integer emotionFlag = esBeanMarkVO.getEmotionFlag();
        Boolean isOriginal = esBeanMarkVO.getIsOriginal();
        String hitWords = esBeanMarkVO.getHitWords();
        String articleId = esBeanMarkVO.getId();
        String md5 = esBeanMarkVO.getMd5();

        Material material = new Material();
        material.setContentId(Long.valueOf(articleId));
        material.setFolderId(Long.valueOf(folderId));
        material.setType(type);
        material.setTypeName(typeName);
        material.setTitle(title);
        material.setText(text);
        material.setUrl(url);
        material.setHost(host);
        material.setAuthor(author);
        material.setEmotionFlag(emotionFlag);
        material.setSiteAreaCodeName(siteAreaCodeName);
        material.setOriginFlag((isOriginal == null || isOriginal) ? "1" : "0");
        material.setHitWords(hitWords);
        material.setPublishTime(publishTime);
        material.setMd5(md5);
        material.setSimilarCount(1);
        return material;
    }

}
