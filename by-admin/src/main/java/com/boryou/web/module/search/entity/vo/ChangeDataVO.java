package com.boryou.web.module.search.entity.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class ChangeDataVO {

    /**
     * 主键id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @TableId(type = IdType.NONE)
    private Long id;

    @NotNull(message = "indexId 不能为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long indexId;

    private String md5;

    @NotNull(message = "changeType 不能为空")
    private Integer changeType;

    @NotBlank(message = "changeValue 不能为空")
    private String changeValue;

}
