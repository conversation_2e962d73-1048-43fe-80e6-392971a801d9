package com.boryou.web.module.chart.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.db.PageResult;
import com.boryou.web.controller.common.entity.BoryouBean;
import com.boryou.web.controller.common.entity.bo.EsSearchBO;
import com.boryou.web.controller.common.util.EsSearchUtil;
import com.boryou.web.domain.vo.AccountInfoVO;
import com.boryou.web.module.chart.domain.SectionData;
import com.boryou.web.module.chart.utils.PropagateUtil;
import com.boryou.web.module.chart.utils.PropagateV2Util;
import com.boryou.web.service.ElasticsearchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-05-14 14:01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PropagateService {
    private final ElasticsearchService elasticsearchService;

    public Map<String, Object> propagateData(EsSearchBO bo) {
        this.buildStartTime(bo);
        String startTime = bo.getStartTime();
        String endTime = bo.getEndTime();
        List<Date> dateAxis = PropagateUtil.getTimeAxis(4, startTime, endTime);

        int pageSize = 20;
        int pageSize5 = pageSize * 5;
        List<SectionData> sections = new ArrayList<>();
        List<AccountInfoVO> accountInfoVOList = new ArrayList<>();
        //3.查出每个时间分隔的数据
        this.getSection(bo, dateAxis, pageSize5, accountInfoVOList, sections);
        Map<String, String> hostAndNameMap = this.getHostAndNameMap(accountInfoVOList);

        List<Map<String, Object>> nodes = new ArrayList<>();
        List<Map<String, Object>> links = new ArrayList<>();
        Map<String, String> hostFilter = new HashMap<>();
        Map<Integer, Integer> rootIndex = new HashMap<>();
        int index = 0;
        int category = 0;
        if (sections.size() > 1) {
            // 找到数量最多的节点所在区域
            int maxPostion = PropagateUtil.getMaxPosition(sections);
            if (maxPostion != 0) {
                // 优先处理最早点
                rootIndex.put(0, index);
                index = PropagateUtil.bean2nodes(dateAxis.get(0), dateAxis.get(1), hostFilter, nodes, links, index, 4,
                        sections.get(0), pageSize, sections.size(), hostAndNameMap);
            }
            // 再处理爆发点，数据量会大一些
            SectionData section = sections.get(maxPostion);
            category = 2;
            rootIndex.put(maxPostion, index);
            index = PropagateUtil.bean2nodes(dateAxis.get(maxPostion), dateAxis.get(maxPostion + 1), hostFilter, nodes, links,
                    index, category, section, pageSize, sections.size(), hostAndNameMap);
            // 对每段内容进行遍历存储（普通节点）
            PropagateUtil.handDateIntevel(dateAxis, sections, hostFilter, nodes, links, maxPostion, index, rootIndex, pageSize,
                    sections.size(), hostAndNameMap);

            // 处理区间根节点之间的连接线
            PropagateUtil.linkRoots(nodes, links, rootIndex);
        }
        Map<String, Object> map = new HashMap<>();
        map.put("nodes", nodes);
        map.put("links", links);
        return map;
    }

    public Map<String, Object> propagateDataV2(EsSearchBO bo) {
        this.buildStartTime(bo);

        String startTime = bo.getStartTime();
        String endTime = bo.getEndTime();
        List<Date> dateAxis = PropagateUtil.getTimeAxis(4, startTime, endTime);

        int pageSize = 20;
        int pageSize5 = pageSize * 5;
        List<SectionData> sections = new ArrayList<>();
        List<AccountInfoVO> accountInfoVOList = new ArrayList<>();
        this.getSection(bo, dateAxis, pageSize5, accountInfoVOList, sections);

        Map<String, String> hostAndNameMap = this.getHostAndNameMap(accountInfoVOList);

        int maxPosition = PropagateV2Util.getMaxPosition(sections);
        PropagateV2Util.processSectionStatus(sections, maxPosition);
        return PropagateV2Util.linkNode(sections, hostAndNameMap);
    }

    private Map<String, String> getHostAndNameMap(List<AccountInfoVO> accountInfoVOList) {
        Map<String, String> hostAndNameMap = new HashMap<>();
        if (CollUtil.isNotEmpty(accountInfoVOList)) {
            hostAndNameMap = accountInfoVOList.stream().distinct()
                    .collect(Collectors.toMap(AccountInfoVO::getDomain, AccountInfoVO::getSector,
                            (oldValue, newValue) -> oldValue));
        }
        return hostAndNameMap;
    }

    private void getSection(EsSearchBO bo, List<Date> dateAxis, int pageSize5, List<AccountInfoVO> accountInfoVOList, List<SectionData> sections) {
        //3.查出每个时间分隔的数据
        if (CollUtil.isNotEmpty(dateAxis) && dateAxis.size() > 1) {
            for (int i = 0; i < dateAxis.size() - 1; i++) {
                // 计算区间的数据 0-1,1-2,2-3,3-4
                Date start = dateAxis.get(i);
                Date end = dateAxis.get(i + 1);
                String startStr = DateUtil.format(start, "yyyy-MM-dd HH:mm:ss");
                String endStr = DateUtil.format(end, "yyyy-MM-dd HH:mm:ss");
                log.warn("start - end: {} - {}", startStr, endStr);
                bo.setStartTime(startStr);
                bo.setEndTime(endStr);
                bo.setPageNum(1);
                bo.setPageSize(pageSize5);
                bo.setSortType(4);
                PageResult<BoryouBean> search = EsSearchUtil.search(bo, EsSearchUtil.SEARCH);
                Map<Integer, List<String>> hostMap = search.stream().filter(s -> s != null && !s.getHost().isEmpty())
                        .collect(Collectors.groupingBy(BoryouBean::getType, Collectors.mapping(BoryouBean::getHost, Collectors.toList())));
                List<AccountInfoVO> accountInfos = elasticsearchService.getNameByHosts(hostMap);
                accountInfoVOList.addAll(accountInfos);

                int total = search.getTotal();
                SectionData sectionData = new SectionData();
                sectionData.setAllNum(total);
                sectionData.setResults(search);
                sections.add(sectionData);
            }
        }
    }

    private void buildStartTime(EsSearchBO bo) {
        String startTime = bo.getStartTime();
        String endTime = bo.getEndTime();
        //1.获取时间范围内有数据的最早时间
        bo.setStartTime(startTime);
        bo.setEndTime(endTime);
        bo.setPageNum(1);
        bo.setPageSize(1);
        bo.setSortType(4);
        PageResult<BoryouBean> searchFast = EsSearchUtil.search(bo, EsSearchUtil.SEARCH);
        if (CollUtil.isNotEmpty(searchFast)) {
            BoryouBean boryouBean = searchFast.get(0);
            if (boryouBean != null) {
                bo.setStartTime(boryouBean.getTime());
            }
        }
    }

}

