package com.boryou.web.module.chart.utils;

import com.boryou.web.controller.common.entity.BoryouBean;
import com.boryou.web.module.chart.domain.SectionData;

import java.util.*;


public class PropagateV2Util {

    private PropagateV2Util() {
        throw new IllegalStateException("Utility class");
    }

    public static int getMaxPosition(List<SectionData> sections) {
        if (sections == null) {
            return 0;
        }
        int maxPosition = 0;
        for (int i = 1; i < sections.size(); i++) {
            if (sections.get(i).getAllNum() > sections.get(maxPosition).getAllNum()) {
                maxPosition = i;
            }
        }
        return maxPosition;
    }

    public static void processSectionStatus(List<SectionData> sections, int maxPosition) {
        if (sections == null || sections.size() == 0) {
            return;
        }
        // 数据量最大的节点在开始，则后面四个点为降温
        if (maxPosition == 0) {
            // 第一个区间爆发
            sections.get(0).setCategory(2);
            // 其他区间降温
            for (int i = 1; i < sections.size(); i++) {
                sections.get(i).setCategory(0);
            }
        } else {
            // 第一区间最早
            sections.get(0).setCategory(4);
            // 最早和最大值中间的点全部升温
            for (int i = 1; i < maxPosition; i++) {
                sections.get(i).setCategory(1);
            }
            sections.get(maxPosition).setCategory(2);
            // 最大值后面的点全部降温
            for (int i = maxPosition + 1; i < sections.size(); i++) {
                sections.get(i).setCategory(0);
            }
        }
    }

    public static Map<String, Object> linkNode(List<SectionData> sections, Map<String, String> hostAndNameMap) {
        int index = 0;// 每条数据的下标
        List<Map<String, Object>> nodes = new ArrayList<>();
        Map<String, Object> linkMap;
        // 各个区间的跟节点
        List<Integer> rootIndexes = new ArrayList<>();
        // 用于判断hostName是否重复Tree
        Set<String> hostNameSet = new HashSet<>();
        // 节点之间的连接关系
        List<Map<String, Object>> links = new ArrayList<>();

        // 先将爆发点的hostName存放是否重复集合中，这样爆发点先处理拥有host优势
        for (SectionData sectionData : sections) {
            if (sectionData.getCategory() == 2) {
                index = processNode(sectionData, hostNameSet, index, rootIndexes, nodes, links, hostAndNameMap);
            }
        }
        // 清楚掉根节点位置，因为爆发点有可能不在第一个位置
        int maxRootIndex = !rootIndexes.isEmpty() ? rootIndexes.get(0) : 0;
        rootIndexes.clear();

        for (SectionData sectionData : sections) {
            // 如果是最早的节点区间，限制转发节点数量不大于10个
            if (sectionData.getCategory() == 4) {
                sectionData.setResults(sectionData.getResults().size() > 10 ? sectionData.getResults().subList(0, 10)
                        : sectionData.getResults());
            }
            // 存放爆发节点位置
            if (sectionData.getCategory() == 2) {
                rootIndexes.add(maxRootIndex);
            } else {
                index = processNode(sectionData, hostNameSet, index, rootIndexes, nodes, links, hostAndNameMap);
            }
        }

        // 处理各个区间的根节点之间的关联
        for (int i = 0; i < rootIndexes.size() - 1; i++) {
            linkMap = new HashMap<>();
            linkMap.put("source", rootIndexes.get(i));
            linkMap.put("target", rootIndexes.get(i + 1));
            links.add(linkMap);
        }

        // 计算总数量
        int count = 0;
        for (SectionData section : sections) {
            count += section.getAllNum();
        }

        // 封装最后的返回值
        Map<String, Object> map = new HashMap<>();
        map.put("nodes", nodes);
        map.put("links", links);
        map.put("countNum", count);
        return map;
    }

    private static int processNode(SectionData sectionData, Set<String> hostNameSet, int index, List<Integer> rootIndexes,
                                   List<Map<String, Object>> nodes, List<Map<String, Object>> links, Map<String, String> hostAndNameMap) {
        String hostName;
        int rootIndex = 0;
        Map<String, Object> linkMap;
        Map<String, Object> nodeMap;
        boolean isTheFirst = true;
        //List<BoryouBean> esYuQingVOList = sectionData.getResults().stream().sorted(Comparator.comparing(BoryouBean::getHotNum,Comparator.nullsFirst(String::compareTo).reversed())).collect(Collectors.toList());
        List<BoryouBean> esYuQingVOList = sectionData.getResults();
        int indexNum = 0;
        for (BoryouBean bean : esYuQingVOList) {
            // host转中文域名
            //hostName = SysSite.getName(bean.getHost());
            hostName = bean.getHost();
            // host不存在则不处理或者有重复，继续下一个
            if (hostName == null || hostName.isEmpty() || hostNameSet.contains(hostName)) {
                continue;
            }
            // 不重复的放入中文域名集合中
            hostNameSet.add(hostName);

            if (isTheFirst) {
                nodeMap = new HashMap<>();
                // 信息的索引下标
                nodeMap.put("index", index);
                rootIndex = index;
                rootIndexes.add(index);
                // 分类
                // 站点中文名
                nodeMap.put("name", hostName);
                // 站点host
                nodeMap.put("siteHost", hostAndNameMap.getOrDefault(hostName, hostName));
                // 是否是该区间的根节点
                nodeMap.put("isRoot", 1);
                // 该区间内数量
                nodeMap.put("value", sectionData.getAllNum());
                nodeMap.put("category", sectionData.getCategory());
                // 第一次使用后标注false
                isTheFirst = false;
            } else {
                if (indexNum == 20) {
                    continue;
                }
                nodeMap = new HashMap<>();
                // 根节点上的普通转发节点
                nodeMap.put("index", index);
                // 3表示转发
                nodeMap.put("category", 3);
                // 站点中文名
                nodeMap.put("name", hostName);
                // 站点host
                nodeMap.put("siteHost", hostAndNameMap.getOrDefault(hostName, hostName));
                // 0代表不是该区间的根节点
                nodeMap.put("isRoot", 0);
                // 节点之间关联
                linkMap = new HashMap<>();
                linkMap.put("source", rootIndex);
                linkMap.put("target", index);
                // 添加到节点连接中
                links.add(linkMap);
                indexNum++;
            }
            index++;
            // 将区间每个节点信息增加到区间的节点集合中
            nodes.add(nodeMap);
        }
        return index;
    }


}
