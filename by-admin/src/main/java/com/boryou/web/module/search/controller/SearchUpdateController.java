package com.boryou.web.module.search.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.core.page.PageDomain;
import com.boryou.common.core.page.TableDataInfo;
import com.boryou.common.core.page.TableSupport;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.common.utils.poi.ExcelUtil;
import com.boryou.common.utils.sql.SqlUtil;
import com.boryou.web.controller.common.enums.EmotionEnum;
import com.boryou.web.domain.vo.SearchVO;
import com.boryou.web.feign.SingleLoginService;
import com.boryou.web.module.search.entity.EsLikeData;
import com.boryou.web.module.search.entity.EsReadData;
import com.boryou.web.module.search.entity.EsSpecialData;
import com.boryou.web.module.search.entity.vo.ChangeDataVO;
import com.boryou.web.module.search.entity.vo.EsSpecialDataVO;
import com.boryou.web.module.search.entity.vo.SpecialExportVO;
import com.boryou.web.module.search.service.ChangeDataService;
import com.boryou.web.module.search.service.EsReadDataService;
import com.boryou.web.module.search.service.EsSpecialDataService;
import com.github.pagehelper.PageHelper;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
public class SearchUpdateController extends BaseController {

    @Resource
    private EsSpecialDataService esSpecialDataService;
    @Resource
    private EsReadDataService esReadDataService;
    @Resource
    private SingleLoginService singleLoginService;
    @Resource
    private AuthenticationManager authenticationManager;
    @Resource
    private ChangeDataService changeDataService;

    @PostMapping("/search/updateEmotion")
    public AjaxResult updateEmotion(@RequestBody SearchVO searchVO) {
        EsSpecialData esSpecialData = new EsSpecialData();
        esSpecialData.setMd5(searchVO.getMd5());
        esSpecialData.setIndexId(searchVO.getIndexId());
        esSpecialData.setCreateTime(searchVO.getCreateTime());
        esSpecialData.setEmotionFlag(Integer.parseInt(searchVO.getEmotionFlag()));
        esSpecialData.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserName());
        return AjaxResult.success("", esSpecialDataService.updateSearchData(esSpecialData));
    }

    @PostMapping("/search/updateTrash")
    public AjaxResult updateTrash(@RequestBody SearchVO searchVO) {
        EsSpecialData esSpecialData = new EsSpecialData();
        esSpecialData.setMd5(searchVO.getMd5());
        esSpecialData.setIndexId(searchVO.getIndexId());
        esSpecialData.setCreateTime(searchVO.getCreateTime());
        esSpecialData.setTrash(Integer.parseInt(searchVO.getNoSpam()));
        esSpecialData.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserName());
        return AjaxResult.success("", esSpecialDataService.updateSearchData(esSpecialData));
    }

    @PostMapping("/search/updateDeal")
    public AjaxResult updateDeal(@RequestBody SearchVO searchVO) {
        EsSpecialData esSpecialData = new EsSpecialData();
        esSpecialData.setMd5(searchVO.getMd5());
        esSpecialData.setDeal(searchVO.getDeal());
        esSpecialData.setIndexId(searchVO.getIndexId());
        esSpecialData.setCreateTime(searchVO.getCreateTime());
        esSpecialData.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserName());
        return AjaxResult.success("", esSpecialDataService.updateSearchData(esSpecialData));
    }

    @PostMapping("/search/updateFollow")
    public AjaxResult updateFollow(@RequestBody SearchVO searchVO) {
        EsSpecialData esSpecialData = new EsSpecialData();
        esSpecialData.setMd5(searchVO.getMd5());
        esSpecialData.setFollow(searchVO.getFollow());
        esSpecialData.setIndexId(searchVO.getIndexId());
        esSpecialData.setCreateTime(searchVO.getCreateTime());
        esSpecialData.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserName());
        return AjaxResult.success("", esSpecialDataService.updateSearchData(esSpecialData));
    }

    @PostMapping("/search/updateChange")
    public AjaxResult updateChange(@RequestBody @Validated ChangeDataVO changeDataVO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        changeDataService.updateChangeData(changeDataVO, user);
        return AjaxResult.success();
    }

    /**
     * 信息报送
     *
     * <AUTHOR>
     * @date 2024/7/23
     */
    @PostMapping("/search/updateWarned")
    public AjaxResult updateWarned(@RequestBody SearchVO searchVO) {
        EsSpecialData esSpecialData = new EsSpecialData();
        esSpecialData.setMd5(searchVO.getMd5());
        esSpecialData.setWarned(searchVO.getWarned());
        esSpecialData.setIndexId(searchVO.getIndexId());
        esSpecialData.setCreateTime(searchVO.getCreateTime());
        esSpecialData.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserName());
        return AjaxResult.success("", esSpecialDataService.updateSearchData(esSpecialData));
    }

    @PostMapping("/search/updateLike")
    public AjaxResult updateLike(@RequestBody EsLikeData data) {
        EsSpecialData esSpecialData = new EsSpecialData();
        esSpecialData.setIndexId(data.getIndexId());
        esSpecialData.setUserLike(1);
        esSpecialData.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserName());
        if (esSpecialDataService.updateSearchData(esSpecialData) > 0) {
            return AjaxResult.success("添加收藏成功", esSpecialDataService.insertLikeData(data));
        }
        return AjaxResult.error();
    }

    @GetMapping("/search/selectLike")
    public AjaxResult selectLike(@ModelAttribute EsLikeData data) {
        startPage();
        return AjaxResult.success("", esSpecialDataService.getLikeDatas(data));
    }

    @PostMapping("/search/read")
    public AjaxResult read(@RequestBody SearchVO searchVO) {
        EsReadData esReadData = new EsReadData();
        esReadData.setIndexId(Long.valueOf(searchVO.getId()));
        esReadData.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserName());
        return AjaxResult.success("", esReadDataService.updateSearchData(esReadData));
    }

    @PostMapping("/search/readMany")
    public AjaxResult readMany(@RequestBody SearchVO searchVO) {
        return AjaxResult.success("", esReadDataService.updateSearchDatas(searchVO.getIds()));
    }

    @PostMapping("/search/selectSpecialPage")
    public TableDataInfo selectSpecialPage(@RequestBody EsSpecialDataVO data) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        PageHelper.startPage(data.getPageNum(), data.getPageSize(), SqlUtil.escapeOrderBySql(pageDomain.getOrderBy()));
        List<EsSpecialDataVO> result = esSpecialDataService.getEsSpecialDataPage(data);
//        TableDataInfo rspData = new TableDataInfo(result, result.getTotal());
//        rspData.setCode(200);
        return getDataTable(result);
    }


    @PostMapping("/search/exportSpecialPage")
    public AjaxResult exportSpecialPage(@RequestBody EsSpecialDataVO data) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        PageHelper.startPage(data.getPageNum(), data.getPageSize(), SqlUtil.escapeOrderBySql(pageDomain.getOrderBy()));
        List<EsSpecialDataVO> result = esSpecialDataService.getEsSpecialDataPage(data);
        List<SpecialExportVO> exportVOList = new ArrayList<>();
        for (EsSpecialDataVO esSpecialDataVO : result) {
            SpecialExportVO specialExportVO = new SpecialExportVO();
            BeanUtil.copyProperties(esSpecialDataVO, specialExportVO, "emotionFlag", "isOriginal", "publishTime", "createTime");
            if (esSpecialDataVO.getEmotionFlag() != null) {
                specialExportVO.setEmotionFlag(EmotionEnum.fromValue(esSpecialDataVO.getEmotionFlag()).getName());
            } else {
                specialExportVO.setEmotionFlag("");
            }
            specialExportVO.setIsOriginal(esSpecialDataVO.getIsOriginal() ? "是" : "否");
            specialExportVO.setHost(esSpecialDataVO.getHostName());
            if (esSpecialDataVO.getPublishTime() != null) {
                specialExportVO.setPublishTime(DateUtil.format(esSpecialDataVO.getPublishTime(), DatePattern.NORM_DATETIME_PATTERN));
            } else {
                specialExportVO.setPublishTime("");
            }
            if (esSpecialDataVO.getCreateTime() != null) {
                specialExportVO.setCreateTime(DateUtil.format(esSpecialDataVO.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN));
            } else {
                specialExportVO.setCreateTime("");
            }

            exportVOList.add(specialExportVO);
        }

        ExcelUtil<SpecialExportVO> util = new ExcelUtil<>(SpecialExportVO.class);
        return util.exportExcel(exportVOList, "导出列表");
    }
}
