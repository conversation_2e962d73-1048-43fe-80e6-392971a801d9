package com.boryou.web.module.chart.domain;

import com.boryou.web.controller.common.entity.BoryouBean;
import lombok.Data;

import java.util.List;

/**
 * 传播路径，用来装载每个小时段内的数据情况
 *
 * <AUTHOR>
 * @date 2015-12-30 下午9:18:20
 */
@Data
public class SectionData {
    private int allNum; // 时段内数据量
    private List<BoryouBean> results; // 时段内前10条数据
    private int category;// 分类等级
    private String startTime;// 开始时间
    private String endTime;// 结束时间
}
