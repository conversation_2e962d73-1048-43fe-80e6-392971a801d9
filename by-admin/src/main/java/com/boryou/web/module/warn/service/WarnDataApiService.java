package com.boryou.web.module.warn.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.boryou.web.controller.common.util.EsSearchUtil;
import com.boryou.web.module.warn.domain.vo.WarnDataVO;
import com.boryou.web.util.JacksonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class WarnDataApiService {

    public List<String> warnDataCheck(WarnDataVO warnDataVO) {
        List<String> result = new ArrayList<>();
        try (HttpResponse response = HttpUtil.createPost(EsSearchUtil.WARN_CHECK)
                .body(JSONUtil.toJsonStr(warnDataVO))
                .timeout(3000000).execute()) {
            if (response.isOk()) {
                result = JSONUtil.toList(response.body(), String.class);
            }
        } catch (Exception e) {
            log.error("WarnApiService.warnDataCheck报错: {}", e.getMessage());
        }
        if (CollUtil.isEmpty(result)) {
            return result;
        }
        List<String> articleIds = warnDataVO.getArticleIds();
        return articleIds.stream().filter(result::contains).collect(Collectors.toList());
    }

    public Map<String, Integer> warnSimilar(WarnDataVO warnDataVO) {
        Map<String, Integer> result = new HashMap<>();
        try (HttpResponse response = HttpUtil.createPost(EsSearchUtil.WARN_SIMILAR)
                .body(JSONUtil.toJsonStr(warnDataVO))
                .timeout(3000000).execute()) {
            if (response.isOk()) {
                result = JacksonUtils.toObj(response.body(), new TypeReference<Map<String, Integer>>() {
                });
            }
        } catch (Exception e) {
            log.error("WarnApiService.warnSimilar: {}", e.getMessage(), e);
        }

        return result;

    }

}
