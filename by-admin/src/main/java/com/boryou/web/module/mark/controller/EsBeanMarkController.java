package com.boryou.web.module.mark.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.common.utils.poi.ExcelUtil;
import com.boryou.web.controller.common.enums.EmotionEnum;
import com.boryou.web.controller.common.enums.MediaTypeEnum;
import com.boryou.web.module.mark.domain.vo.EsBeanMarkPage;
import com.boryou.web.module.mark.domain.vo.EsBeanMarkVO;
import com.boryou.web.module.mark.service.EsBeanMarkService;
import com.boryou.web.module.warn.domain.vo.WarnExportVO;
import com.boryou.web.module.warn.service.WarnDataService;
import com.boryou.web.service.ElasticsearchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequiredArgsConstructor
public class EsBeanMarkController {

    private final EsBeanMarkService esBeanMarkService;
    private final WarnDataService warnDataService;
    private final ElasticsearchService elasticsearchService;

    /**
     * 分页查询EsBeanMark信息
     *
     * @param esBeanMarkVO 查询参数
     * @return 分页结果
     */
    @PostMapping("/esBeanMark/list")
    public AjaxResult getEsBeanMarkList(@RequestBody @Validated EsBeanMarkPage esBeanMarkPage) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Page<EsBeanMarkVO> page = esBeanMarkService.getEsBeanMarkPage(esBeanMarkPage, user);
        return AjaxResult.success(page);
    }

    /**
     * 添加或更新EsBeanMark信息
     *
     * @param esBeanMarkVO EsBeanMark信息
     * @return 操作结果
     */
    @PostMapping("/esBeanMark/save")
    public AjaxResult saveEsBeanMark(@RequestBody @Validated EsBeanMarkVO esBeanMarkVO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        boolean result = esBeanMarkService.saveOrUpdateEsBeanMark(esBeanMarkVO, user);
        return AjaxResult.optionResult(result);
    }

    /**
     * 删除EsBeanMark信息
     *
     * @param esBeanMarkVO 删除参数
     * @return 操作结果
     */
    @PostMapping("/esBeanMark/delete")
    public AjaxResult deleteEsBeanMark(@RequestBody EsBeanMarkVO esBeanMarkVO) {
        boolean result = esBeanMarkService.deleteEsBeanMark(esBeanMarkVO);
        return AjaxResult.optionResult(result);
    }

    @PostMapping("/esBeanMark/export")
    public AjaxResult esBeanMarkExport(@RequestBody EsBeanMarkPage esBeanMarkPage) {
        esBeanMarkService.judgeIds(esBeanMarkPage);

        SysUser user = SecurityUtils.getLoginUser().getUser();

        Page<EsBeanMarkVO> markPage = esBeanMarkService.getEsBeanMarkPage(esBeanMarkPage, user);
        List<EsBeanMarkVO> records = markPage.getRecords();

        List<WarnExportVO> esBeanExportVOList = new ArrayList<>();

        List<String> host = records.stream().map(EsBeanMarkVO::getHost).filter(s -> s != null && !s.isEmpty()).collect(Collectors.toList());
        Map<String, String> hostAndNameMap = elasticsearchService.getDomainMap(host);

        for (EsBeanMarkVO bean : records) {
            WarnExportVO esBeanExportVO = new WarnExportVO();
            BeanUtil.copyProperties(bean, esBeanExportVO, "siteMeta", "emotionFlag", "isOriginal", "isSpam");
            if (CollUtil.isNotEmpty(bean.getSiteMeta())) {
                esBeanExportVO.setSiteMeta(CollUtil.join(bean.getSiteMeta(), ","));
            }
            esBeanExportVO.setEmotionFlag(EmotionEnum.fromValue(bean.getEmotionFlag()).getName());
            esBeanExportVO.setIsOriginal(bean.getIsOriginal() ? "是" : "否");
            esBeanExportVO.setIsSpam(bean.getIsSpam() ? "是" : "否");
            //站点名称
            if (hostAndNameMap.containsKey(bean.getHost())) {
                //双微显示作者
                if (bean.getType() == MediaTypeEnum.WECHAT.getValue() || bean.getType() == MediaTypeEnum.WEIBO.getValue()) {
                    esBeanExportVO.setHost(bean.getAuthor());
                } else {
                    esBeanExportVO.setHost(hostAndNameMap.get(bean.getHost()));
                }
            }
            esBeanExportVOList.add(esBeanExportVO);
        }
        ExcelUtil<WarnExportVO> util = new ExcelUtil<>(WarnExportVO.class);
        return util.exportExcel(esBeanExportVOList, "导出列表");
    }

    @PostMapping("/esBeanMark/material/add")
    public AjaxResult addMaterial(@RequestBody EsBeanMarkPage esBeanMarkPage) {
        esBeanMarkService.judgeIds(esBeanMarkPage);

        SysUser user = SecurityUtils.getLoginUser().getUser();

        Page<EsBeanMarkVO> markPage = esBeanMarkService.getEsBeanMarkPage(esBeanMarkPage, user);

        List<EsBeanMarkVO> records = markPage.getRecords();

        boolean b = esBeanMarkService.addMaterial(records, esBeanMarkPage);
        return AjaxResult.optionResult(b);
    }

}
