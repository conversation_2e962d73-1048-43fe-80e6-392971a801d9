package com.boryou.web.controller.search;

import cn.hutool.core.util.StrUtil;
import cn.hutool.db.PageResult;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.boryou.common.annotation.Log;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.enums.BusinessType;
import com.boryou.common.utils.RedisUtil;
import com.boryou.web.controller.common.entity.BoryouBean;
import com.boryou.web.controller.common.entity.EsBean;
import com.boryou.web.controller.common.entity.bo.EsSearchBO;
import com.boryou.web.controller.common.util.ConvertHandler;
import com.boryou.web.controller.common.util.EsSearchUtil;
import com.boryou.web.domain.Plan;
import com.boryou.web.domain.vo.GraphModelVO;
import com.boryou.web.domain.vo.SearchVO;
import com.boryou.web.module.chart.service.PropagateService;
import com.boryou.web.service.PlanService;
import com.boryou.web.service.SearchAnalyseService;
import com.boryou.web.util.DoubaoUtil;
import com.hankcs.hanlp.HanLP;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Slf4j
@RestController()
public class SearchAnalyseController {

    @Resource
    private SearchAnalyseService searchAnalyseService;
    @Resource
    private PlanService planService;
    @Resource
    private ConvertHandler convertHandler;
    @Autowired
    private RedisUtil redisUtil;
    @Qualifier("redisTemplate")
    @Autowired
    private RedisTemplate redisTemplate;
    @Resource
    private PropagateService propagateService;

    /**
     * 敏感占比图
     */
    @PostMapping("/analyse/emotionAnalyse")
    public AjaxResult emotionAnalyse(@RequestBody SearchVO searchVO) {
        EsSearchBO bo = convertHandler.getEsSearchBOForAnalyse(searchVO);
        JSONObject emotionAnalyse = searchAnalyseService.getEmotionAnalyse(bo);
        JSONObject desc = emotionAnalyse.getJSONObject("desc");
        if (searchVO.getPlanId() == null) {
            desc.putOnce("planName", "“" + searchVO.getKeyWord1() + "”");
        } else {
            Plan plan = planService.selectPlanById(searchVO.getPlanId());
            desc.putOnce("planName", plan.getPlanName());
        }
        return AjaxResult.success("", emotionAnalyse);
    }

    /**
     * 敏感占比TOP10
     */
    @PostMapping("/analyse/emotionAnalyseTop")
    public AjaxResult emotionAnalyseTop(@RequestBody SearchVO searchVO) {
        searchVO.setSort(7);
        searchVO.setPageSize(10);
        searchVO.setEmotionFlag("1");
        EsSearchBO bo = convertHandler.getEsSearchBOForAnalyse(searchVO);
        PageResult<EsBean> pageResult = EsSearchUtil.searchEsBeanList(bo);
        return AjaxResult.success(pageResult);
    }

    /**
     * 信息来源占比
     */
    @PostMapping("/analyse/mediaTypeAnalyse")
    public AjaxResult mediaTypeAnalyse(@RequestBody SearchVO searchVO) {
        EsSearchBO bo = convertHandler.getEsSearchBOForAnalyse(searchVO);
        return AjaxResult.success("", searchAnalyseService.mediaTypeAnalyse(bo));
    }

    /**
     * 关键词云
     */
    @PostMapping("/analyse/wordAnalyse")
    public AjaxResult wordAnalyse(@RequestBody SearchVO searchVO) {
        searchVO.setSort(3);
        searchVO.setPageSize(200);
        EsSearchBO bo = convertHandler.getEsSearchBOForAnalyse(searchVO);
        return AjaxResult.success("", searchAnalyseService.wordAnalyse(bo, searchVO.getPlanId()));
    }

    /**
     * 媒体活跃统计
     */
    @PostMapping("/analyse/mediaActiveMap")
    public AjaxResult mediaActiveMap(@RequestBody SearchVO searchVO) {
        EsSearchBO bo = convertHandler.getEsSearchBOForAnalyse(searchVO);
        JSONArray objects = searchAnalyseService.mediaActiveMap(bo);
        return AjaxResult.success("", objects);
    }

    /**
     * 地域分布统计
     */
    @PostMapping("/analyse/areaMap")
    public AjaxResult areaMap(@RequestBody SearchVO searchVO) {
        EsSearchBO bo = convertHandler.getEsSearchBOForAnalyse(searchVO);
        return AjaxResult.success("", searchAnalyseService.areaMap(bo));
    }

    @Log(title = "方案统计分析", businessType = BusinessType.QUERY)
    @PostMapping("/analyse/time/type")
    public AjaxResult timeType(@RequestBody SearchVO searchVO) {
        EsSearchBO bo = convertHandler.getEsSearchBOForAnalyse(searchVO);
        GraphModelVO graphModelVO = searchAnalyseService.timeType(bo);
        return AjaxResult.success(graphModelVO);
    }

    @PostMapping("/analyse/time/emotion")
    public AjaxResult timeEmotion(@RequestBody SearchVO searchVO) {
        EsSearchBO bo = convertHandler.getEsSearchBOForAnalyse(searchVO);
        GraphModelVO graphModelVO = searchAnalyseService.timeEmotion(bo);
        return AjaxResult.success(graphModelVO);
    }

    /**
     * 事件摘要
     */
    @PostMapping("/analyse/summary")
    public AjaxResult summary(@RequestBody SearchVO searchVO) {
        searchVO.setPageSize(3);
        EsSearchBO bo = convertHandler.getEsSearchBOForAnalyse(searchVO);
        PageResult<BoryouBean> search = EsSearchUtil.search(bo, EsSearchUtil.SEARCH);
        List<String> collect = search.stream().map(BoryouBean::getText).collect(Collectors.toList());
        String summary = "";
        for (int i = 0; i < collect.size() && i < 3; i++) {
            if (StrUtil.isEmpty(collect.get(i))) {
                continue;
            }
            String join = search.get(i).getTime() + "。"+ collect.get(i);
            summary = DoubaoUtil.summary(join, 0);
            if (!summary.equals("") && summary.contains("<br/>")) {
                break;
            }
            if (!summary.contains("<br/>") && (i >= collect.size()-1 || i >= 2) ) {
                //使用hanlp生成摘要
                summary = HanLP.getSummary(join, 300);
            }
        }
        return AjaxResult.success("", summary);
    }


    /**
     * 敏感占比图   媒体账号  3，5，6，11    媒体网站 :1
     */
    @PostMapping("/analyse/media/level")
    public AjaxResult mediaLevel(@RequestBody SearchVO searchVO) {
        EsSearchBO bo = convertHandler.getEsSearchBOForAnalyse(searchVO);
        return AjaxResult.success("", searchAnalyseService.mediaLevel(bo));
    }

    /**
     * 央级媒体数量
     */
    @PostMapping("/analyse/media/central")
    public AjaxResult mediaCentral(@RequestBody SearchVO searchVO) {
        EsSearchBO bo = convertHandler.getEsSearchBOForAnalyse(searchVO);
        JSONObject entries = searchAnalyseService.mediaCentral(bo);
        return AjaxResult.success("", entries);
    }

    /**
     * 获取方案相关热搜
     */
    @PostMapping("/analyse/hotsearch")
    public AjaxResult hotsearch(@RequestBody SearchVO searchVO) {
        return AjaxResult.success("", searchAnalyseService.hotsearch(searchVO));
    }

    /**
     * 获取方案媒体观点
     */
    @PostMapping("/analyse/mediaOpinion")
    public AjaxResult mediaOpinion(@RequestBody SearchVO searchVO) {
        return AjaxResult.success("", searchAnalyseService.mediaOpinion(searchVO));
    }

    /**
     * 获取方案网民观点
     */
    @PostMapping("/analyse/netizenOpinion")
    public AjaxResult netizenOpinion(@RequestBody SearchVO searchVO) {
        return AjaxResult.success("", searchAnalyseService.netizenOpinion(searchVO));
    }

    @PostMapping("/analyse/createReport")
    public void createReport(HttpServletResponse response, @RequestBody SearchVO searchVO) {
        searchAnalyseService.createReport(response, searchVO);
    }

    @PostMapping("/analyse/updateReport")
    public void updateReport(HttpServletResponse response, @RequestBody SearchVO searchVO) {
        searchAnalyseService.updateReport(response, searchVO);
    }

    /**
     * 统计分析-对外分享二维码保存条件
     */
    @PostMapping("/analyse/saveAnalyseCondition")
    public AjaxResult saveAnalyseCondition(@RequestBody JSONObject searchVO, HttpServletRequest request) {
        searchAnalyseService.saveFilter(searchVO, request);
        return AjaxResult.success();
    }

    /**
     * 统计分析-对外分享二维码保存条件
     */
    @GetMapping("/analyse/getAnalyseCondition")
    public AjaxResult getAnalyseCondition(HttpServletRequest request, String planId) {
        JSONObject res = searchAnalyseService.getAnalyseCondition(request, planId);
        return AjaxResult.success(res);
    }

    @PostMapping("/analyse/propagate")
    public AjaxResult propagate(@RequestBody SearchVO searchVO) {
        EsSearchBO bo = convertHandler.getEsSearchBOForAnalyse(searchVO);
        return AjaxResult.success(propagateService.propagateDataV2(bo));
    }

}
