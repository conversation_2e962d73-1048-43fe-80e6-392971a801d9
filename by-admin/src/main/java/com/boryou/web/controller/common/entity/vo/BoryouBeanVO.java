package com.boryou.web.controller.common.entity.vo;

import com.boryou.web.controller.common.entity.BoryouBean;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024-05-23 10:26
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BoryouBeanVO extends BoryouBean {
    private String address;
    /**
     * 是否被标记为垃圾内容*
     */
    private Boolean isSpam;
    /**
     * 处置 0 无 1 已处置
     */
    private int deal;

    /**
     * 重点关注 0 无 1 已关注
     */
    private int follow;

    /**
     * 预警 0 无 1 已预警
     */
    private int warned;
    private int riskGrade;
}

